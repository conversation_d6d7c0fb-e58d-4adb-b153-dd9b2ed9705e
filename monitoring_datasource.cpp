#include "monitoring_datasource.h"
#include "csvreader.h"
#include <QVariantMap>
#include <QtCharts/QXYSeries>
#include <QtCharts/QLineSeries>
#include <QCoreApplication>
#include <QtCharts/QAbstractSeries>
#include <QDateTime>
#include <QTimer>
#include <QMutexLocker>
#include <QThreadPool>
#include <QRunnable>
#include <QMetaObject>
#include <thread>
#include <chrono>


// Qt 5.x需要使用QtCharts命名空间
QT_CHARTS_USE_NAMESPACE

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

// BoilerSwitchTask类已移除，因为使用简化的锅炉切换逻辑

// 条件编译：启用硬件数据采集
#ifndef ENABLE_HARDWARE_DATA
#define ENABLE_HARDWARE_DATA
#endif
#ifdef ENABLE_HARDWARE_DATA
#include "smoke_analyzer_comm.h"
#include "boiler.h"
#include "config_manager.h"
#endif

MonitoringDataSource::MonitoringDataSource(QObject *parent)
    : QObject(parent)
    , m_timer(new QTimer(this))
    , m_buffer60Min(BUFFER_60MIN)
    , m_buffer8Hour(BUFFER_8HOUR)
    , m_buffer12Hour(BUFFER_12HOUR)
    , m_buffer24Hour(BUFFER_24HOUR)
    , m_dataStartTime()
    , m_dataStartTimeSet(false)
    , m_tableUpdateCounter(0)
    , m_isBackflowActive(false)
    , m_isDataUpdateSuspended(false)
    , m_backflowDelayTimer(new QTimer(this))
    , m_backflowDelayTime(60)
    , m_suspendedO2Value("0.00%")
    , m_suspendedCOValue("0ppm")
    , m_isRunning(false)
    , m_isDataConnected(false)
    , m_connectionStatus("未连接串口数据采集设备")
    , m_currentTemperature("0.0℃")
    , m_currentVoltage("0.0kPa")
    , m_currentCurrent("0.000A")
    , m_csvReader(new CsvReader(this))
    , m_isHistoryMode(false)
    , m_currentHistoryDate()
{
    connect(m_timer, &QTimer::timeout, this, &MonitoringDataSource::updateData);
    // 不设置初始间隔，等待从配置文件读取

    // 初始化反吹反馈延迟定时器
    m_backflowDelayTimer->setSingleShot(true);
    connect(m_backflowDelayTimer, &QTimer::timeout, this, &MonitoringDataSource::resumeO2COUpdates);

    // 加载锅炉列表
    loadBoilerList();

    // 设置初始定时器间隔（从配置文件读取）
    updateTimerInterval();

    // 循环缓冲区已在初始化列表中完成初始化
}

void MonitoringDataSource::setIsRunning(bool running)
{
    if (m_isRunning != running) {
        m_isRunning = running;
        emit isRunningChanged();

        if (running) {
            startMonitoring();
        } else {
            stopMonitoring();
        }
    }
}

void MonitoringDataSource::setCurrentBoiler(const QString &boiler)
{
    if (m_currentBoiler != boiler) {
        debug_printf("切换烟气分析仪: 从 '%s' 到 '%s'\n",
                    m_currentBoiler.toStdString().c_str(), boiler.toStdString().c_str());

        // 简化的烟气分析仪切换逻辑，因为每个设备都有独立线程在采集数据
        m_currentBoiler = boiler;

        // 更新定时器间隔以匹配新锅炉的采集间隔
        updateTimerInterval();

        // 立即发射信号，让UI知道设备已经改变
        emit currentBoilerChanged();

        // 清空表格数据，准备显示新设备的数据
        m_smokeTableData.clear();
        m_tableUpdateCounter = 0;  // 重置表格更新计数器

        // 重置相对时间轴的开始时间
        m_dataStartTimeSet = false;

        // 重置连接状态，让下次updateData时重新检测
        m_isDataConnected = false;
        m_connectionStatus = "正在检测新烟气分析仪的串口连接...";
        emit dataConnectionChanged();

        // 发射数据变化信号
        emit smokeDataChanged();
        emit smokeTableDataChanged();
        emit chartDataUpdated();

        // 如果监控正在运行，立即检测新设备的连接状态
        if (m_isRunning) {
            updateData();
        }
    }
}

void MonitoringDataSource::startMonitoring()
{
    if (!m_timer->isActive()) {
        m_timer->start();
        m_isRunning = true;
        emit isRunningChanged();

        // 初始化连接状态检查
        m_isDataConnected = false;
        m_connectionStatus = "正在检测串口数据采集设备...";
        emit dataConnectionChanged();

        // 立即进行一次数据更新和连接状态检测，不等待定时器
        debug_printf("UI监控启动: 立即进行首次连接状态检测\n");
        updateData();

        // 基于配置的动态延迟更新数据，确保图表能正常显示
        int collectionInterval = getCurrentCollectionInterval();
        int firstDelay = collectionInterval * 1000;      // 第一次延迟：1个采集周期
        int secondDelay = collectionInterval * 1500;     // 第二次延迟：1.5个采集周期

        debug_printf("UI监控: 使用动态延迟 - 第一次=%d毫秒, 第二次=%d毫秒\n",
                    firstDelay, secondDelay);

        QTimer::singleShot(firstDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%d秒后再次更新数据\n", collectionInterval);
            updateData();
        });

        QTimer::singleShot(secondDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%.1f秒后第三次更新数据\n", collectionInterval * 1.5);
            updateData();
        });
    }
}



void MonitoringDataSource::stopMonitoring()
{
    if (m_timer->isActive()) {
        m_timer->stop();
        m_isRunning = false;
        emit isRunningChanged();

        // 停止监控时重置连接状态
        m_isDataConnected = false;
        m_connectionStatus = "数据监控已停止";
        emit dataConnectionChanged();
    }
}

int MonitoringDataSource::getCurrentCollectionInterval() const
{
    // 获取当前锅炉的采集间隔
    int interval = 15;  // 默认值

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            interval = g_config_manager->get<int>(m_currentBoiler.toStdString(), "CollectionInterval", 15);
        }
    }

    return interval;
}

int MonitoringDataSource::getMaxWindowDataPoints(int timeRangeMinutes) const
{
    // 计算指定时间窗口需要的最大数据点数
    int intervalSeconds = getCurrentCollectionInterval();
    int windowSeconds = timeRangeMinutes * 60;  // 转换为秒
    int maxPoints = windowSeconds / intervalSeconds + 1;  // +1 确保覆盖完整窗口

    // 根据时间范围限制在对应缓冲区大小内
    int maxBufferSize;
    switch (timeRangeMinutes) {
        case 60:    // 1小时
            maxBufferSize = BUFFER_60MIN;
            break;
        case 480:   // 8小时
            maxBufferSize = BUFFER_8HOUR;
            break;
        case 720:   // 12小时
            maxBufferSize = BUFFER_12HOUR;
            break;
        case 1440:  // 24小时
            maxBufferSize = BUFFER_24HOUR;
            break;
        default:
            maxBufferSize = BUFFER_60MIN;  // 默认1小时
            break;
    }

    return qMin(maxPoints, maxBufferSize);
}

// 获取指定时间范围对应的缓冲区
MonitoringDataSource::TimeRangeBuffer& MonitoringDataSource::getBufferForTimeRange(int timeRangeMinutes)
{
    switch (timeRangeMinutes) {
        case 60:    // 1小时
            return m_buffer60Min;
        case 480:   // 8小时
            return m_buffer8Hour;
        case 720:   // 12小时
            return m_buffer12Hour;
        case 1440:  // 24小时
            return m_buffer24Hour;
        default:
            // 默认返回1小时缓冲区
            return m_buffer60Min;
    }
}

// 向指定缓冲区添加数据点
void MonitoringDataSource::addDataPointToBuffer(TimeRangeBuffer& buffer, double o2, double co, double temperature, double voltage, double current, int switch1)
{
    // 添加新数据点到循环缓冲区，在当前索引位置写入新数据
    DataPoint &point = buffer.dataBuffer[buffer.currentIndex];
    point.o2 = o2;
    point.co = co;
    point.temperature = temperature;
    point.voltage = voltage;
    point.current = current;
    point.switch1 = switch1;

    // 更新索引和计数
    buffer.currentIndex = (buffer.currentIndex + 1) % buffer.maxSize;

    if (buffer.dataCount < buffer.maxSize) {
        buffer.dataCount++;  // 还没满，继续增加
    }
    // 满了后，dataCount保持maxSize，实现滑动窗口
}

// 从指定缓冲区获取数据
QVariantList MonitoringDataSource::getDataFromBuffer(const TimeRangeBuffer& buffer, const QString& dataType, int requestedMinutes) const
{
    QVariantList result;

    // 固定3秒采集间隔
    const double SAMPLE_INTERVAL_MINUTES = 3.0 / 60.0;  // 3秒 = 0.05分钟

    // 计算需要的数据点数
    int requestedPoints = qMin(requestedMinutes * 60 / 3, buffer.dataCount);  // 每3秒一个点

    for (int i = 0; i < requestedPoints; ++i) {
        int index = (buffer.currentIndex - requestedPoints + i + buffer.maxSize) % buffer.maxSize;
        const DataPoint &point = buffer.dataBuffer[index];

        // 按3秒间隔计算时间：0, 0.05, 0.1, 0.15... 分钟
        double timeMinutes = i * SAMPLE_INTERVAL_MINUTES;  // 从0开始的相对时间
        double timeHours = timeMinutes / 60.0;

        QVariantMap dataPoint;
        dataPoint["x"] = timeHours;  // 小时格式（用于兼容性）
        dataPoint["x_minutes"] = timeMinutes;  // 分钟格式

        if (dataType == "o2") {
            dataPoint["y"] = point.o2;
        } else if (dataType == "co") {
            dataPoint["y"] = point.co;
        } else if (dataType == "switch1") {
            dataPoint["y"] = point.switch1;
        }

        result.append(dataPoint);
    }

    return result;
}

int MonitoringDataSource::getBackflowDelayTime() const
{
    // 从配置文件读取反吹反馈延迟时间
    int delayTime = 60;  // 默认60秒

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            delayTime = g_config_manager->get<int>(m_currentBoiler.toStdString(), "BackflowDelayTime", 60);
        }
    }

    return delayTime;
}

void MonitoringDataSource::checkBackflowStatus(int switch1)
{
    bool currentBackflowActive = (switch1 == 1);  // 修正：1表示反吹运行，0表示停止

    // 检查反吹反馈状态是否发生变化
    if (currentBackflowActive != m_isBackflowActive) {
        m_isBackflowActive = currentBackflowActive;

        if (m_isBackflowActive) {
            // 反吹反馈开始运行，暂停氧气和一氧化碳数值更新
            suspendO2COUpdates();
            debug_printf("监控系统: 检测到反吹反馈开始运行，暂停氧气和一氧化碳数值更新\n");
        } else {
            // 反吹反馈停止，启动延迟恢复定时器
            m_backflowDelayTime = getBackflowDelayTime();
            m_backflowDelayTimer->start(m_backflowDelayTime * 1000);  // 转换为毫秒
            debug_printf("监控系统: 检测到反吹反馈停止，将在%d秒后恢复氧气和一氧化碳数值更新\n", m_backflowDelayTime);
        }
    }
}

void MonitoringDataSource::suspendO2COUpdates()
{
    if (!m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = true;

        // 停止延迟恢复定时器（如果正在运行）
        if (m_backflowDelayTimer->isActive()) {
            m_backflowDelayTimer->stop();
        }

        // 保存当前的O2和CO数值作为暂停前的最后数值
        // 注意：这里的数值会在checkBackflowStatus调用之前通过updateSmokeData获取到
        debug_printf("监控系统: 氧气和一氧化碳数值更新已暂停，保存的暂停前数值 - O2: %s, CO: %s\n",
                    m_suspendedO2Value.toStdString().c_str(), m_suspendedCOValue.toStdString().c_str());
    }
}

void MonitoringDataSource::resumeO2COUpdates()
{
    if (m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = false;
        debug_printf("监控系统: 延迟%d秒后，氧气和一氧化碳数值更新已恢复\n", m_backflowDelayTime);

        // 立即触发一次数据更新，以显示最新的氧气和一氧化碳数值
        updateData();
    }
}

void MonitoringDataSource::clearData()
{
    // 清空内存循环缓冲区
    clearMemoryData();

    // 清空表格数据和UI状态
    m_smokeTableData.clear();
    m_tableUpdateCounter = 0;  // 重置表格更新计数器

    emit smokeDataChanged();
    emit smokeTableDataChanged();
    emit chartDataUpdated();

    debug_printf("内存数据已清空\n");
}

// ==================== 重构：从内存循环缓冲区读取数据 ====================

QVariantList MonitoringDataSource::smokeO2Data() const
{
    // 默认返回1小时的数据
    return getSmokeO2Data(60);
}

QVariantList MonitoringDataSource::getSmokeO2Data(int timeRangeMinutes) const
{
    const TimeRangeBuffer& buffer = const_cast<MonitoringDataSource*>(this)->getBufferForTimeRange(timeRangeMinutes);
    return getDataFromBuffer(buffer, "o2", timeRangeMinutes);
}

QVariantList MonitoringDataSource::smokeCOData() const
{
    // 默认返回1小时的数据
    return getSmokeCOData(60);
}

QVariantList MonitoringDataSource::getSmokeCOData(int timeRangeMinutes) const
{
    const TimeRangeBuffer& buffer = const_cast<MonitoringDataSource*>(this)->getBufferForTimeRange(timeRangeMinutes);
    return getDataFromBuffer(buffer, "co", timeRangeMinutes);
}

QVariantList MonitoringDataSource::smokeSwitch1Data() const
{
    // 默认返回1小时的数据
    return getSmokeSwitch1Data(60);
}

QVariantList MonitoringDataSource::getSmokeSwitch1Data(int timeRangeMinutes) const
{
    const TimeRangeBuffer& buffer = const_cast<MonitoringDataSource*>(this)->getBufferForTimeRange(timeRangeMinutes);
    return getDataFromBuffer(buffer, "switch1", timeRangeMinutes);
}

// ==================== 内存数据管理方法 ====================

void MonitoringDataSource::addDataPoint(double o2, double co, double temperature, double voltage, double current, int switch1)
{
    // 同时向所有时间范围的缓冲区添加数据
    addDataPointToBuffer(m_buffer60Min, o2, co, temperature, voltage, current, switch1);
    addDataPointToBuffer(m_buffer8Hour, o2, co, temperature, voltage, current, switch1);
    addDataPointToBuffer(m_buffer12Hour, o2, co, temperature, voltage, current, switch1);
    addDataPointToBuffer(m_buffer24Hour, o2, co, temperature, voltage, current, switch1);

    debug_printf("添加数据点到所有缓冲区: O2=%.2f, CO=%.0f, 60min=%d/%d, 8h=%d/%d, 12h=%d/%d, 24h=%d/%d\n",
                o2, co,
                m_buffer60Min.dataCount, m_buffer60Min.maxSize,
                m_buffer8Hour.dataCount, m_buffer8Hour.maxSize,
                m_buffer12Hour.dataCount, m_buffer12Hour.maxSize,
                m_buffer24Hour.dataCount, m_buffer24Hour.maxSize);
}

void MonitoringDataSource::clearMemoryData()
{
    // 清空所有时间范围的缓冲区
    m_buffer60Min.currentIndex = 0;
    m_buffer60Min.dataCount = 0;

    m_buffer8Hour.currentIndex = 0;
    m_buffer8Hour.dataCount = 0;

    m_buffer12Hour.currentIndex = 0;
    m_buffer12Hour.dataCount = 0;

    m_buffer24Hour.currentIndex = 0;
    m_buffer24Hour.dataCount = 0;

    debug_printf("所有时间范围的内存循环缓冲区已清空\n");
}

void MonitoringDataSource::updateData()
{
    static QDateTime lastUpdateTime = QDateTime::currentDateTime();
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeDiff = lastUpdateTime.msecsTo(currentTime);

    debug_printf("UI数据更新调用 - 距离上次更新: %lld毫秒, 定时器间隔: %d毫秒\n",
                timeDiff, m_timer->interval());

    updateSmokeData();

    lastUpdateTime = currentTime;
}

void MonitoringDataSource::updateSmokeData()
{
    // 检查是否有选择的烟气分析仪
    if (m_currentBoiler.isEmpty()) {
        return;
    }

    // 从硬件获取真实烟气数据
    float o2 = 0.0f, co = 0.0f;
    float current = 0.0f, voltage = 0.0f, temperature = 0.0f;
    int switch1 = 1;  // 开关量信号，默认关闭状态
    bool hardwareConnected = false;

    // 检查硬件连接状态
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    std::string deviceName = m_currentBoiler.toStdString();

    auto it = boiler_map.find(deviceName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->fd >= 0) {
        hardwareConnected = true;

        // 立即获取数据，不等待data_ready标志
        // 这样可以让UI更快地显示已采集到的参数值
        try {
            get_realtime_data(deviceName, &co, &o2, &current, &voltage, &temperature, &switch1);

            // 在检查反吹状态之前，先保存当前的O2和CO数值（作为潜在的暂停前数值）
            if (!m_isDataUpdateSuspended) {
                // 只在数据更新未暂停时更新暂停前的数值
                m_suspendedO2Value = QString::number(o2, 'f', 2) + "%";
                m_suspendedCOValue = QString::number(co, 'f', 0) + "ppm";
            }

            // 检查反吹反馈状态并处理氧气和一氧化碳数值更新控制
            checkBackflowStatus(switch1);

            // 如果数据更新被暂停，记录日志
            if (m_isDataUpdateSuspended) {
                debug_printf("监控系统: 反吹反馈运行中，氧气和一氧化碳数值保持暂停状态 - O2: %s, CO: %s\n",
                            m_suspendedO2Value.toStdString().c_str(), m_suspendedCOValue.toStdString().c_str());
            }

            // 如果是首次获取到有效数据，记录日志
            if (!it->second->data_ready && (o2 > 0 || co > 0)) {
                debug_printf("UI开始显示烟气分析仪 %s 的部分数据\n", deviceName.c_str());
            }
        } catch (...) {
            // 如果数据采集函数出现异常，设置为无效数据
            debug_printf("烟气数据获取异常: %s\n", deviceName.c_str());
            o2 = co = current = voltage = temperature = 0.0f;
            switch1 = 0;  // 异常时设置为停止状态
        }
    }
#endif

    // 更新连接状态
    if (hardwareConnected) {
        // 硬件连接时直接使用真实数据，不做额外的有效性判断
        // 更新连接状态
        if (!m_isDataConnected) {
            m_isDataConnected = true;
            m_connectionStatus = "烟气分析仪已连接";
            debug_printf("UI连接状态: 烟气分析仪已连接\n");
            emit dataConnectionChanged();
        }


        // 获取当前时间
        QDateTime now = QDateTime::currentDateTime();

        // 如果是第一次采集数据，设置开始时间
        if (!m_dataStartTimeSet) {
            m_dataStartTime = now;
            m_dataStartTimeSet = true;
        }

        // 获取当前锅炉的采集间隔配置
        int collectionIntervalSeconds = 15; // 默认15秒
#ifdef ENABLE_HARDWARE_DATA
        extern std::unordered_map<std::string, Boiler*> boiler_map;
        extern ConfigManager* g_config_manager;

        std::string boilerName = m_currentBoiler.toStdString();
        auto it = boiler_map.find(boilerName);
        if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
            collectionIntervalSeconds = it->second->collection_interval;
        } else if (g_config_manager && g_config_manager->exists(boilerName, "CollectionInterval")) {
            collectionIntervalSeconds = g_config_manager->get<int>(boilerName, "CollectionInterval", 15);
        }
#endif

        // 使用采集间隔（避免编译器警告）
        Q_UNUSED(collectionIntervalSeconds);

        // 计算基于数据开始时间的相对时间
        // 确保时间轴从0开始，并且严格按照实际时间流逝计算
        QDateTime currentTime = QDateTime::currentDateTime();
        if (!m_dataStartTimeSet) {
            m_dataStartTime = currentTime;
            m_dataStartTimeSet = true;
            debug_printf("数据开始时间已设置: %s\n", m_dataStartTime.toString("yyyy-MM-dd HH:mm:ss").toStdString().c_str());
        }

        // 计算从数据开始到现在的实际时间差
        qint64 elapsedSeconds = m_dataStartTime.secsTo(currentTime);
        double relativeTimeHours = elapsedSeconds / 3600.0;  // 转换为小时
        double relativeTimeMinutes = elapsedSeconds / 60.0;  // 转换为分钟

        // 添加数据到内存循环缓冲区
        if (m_isDataUpdateSuspended) {
            // 反吹期间：使用暂停前的O2和CO数值
            QString suspendedO2Str = m_suspendedO2Value;
            QString suspendedCOStr = m_suspendedCOValue;
            suspendedO2Str.remove("%");
            suspendedCOStr.remove("ppm");

            double suspendedO2 = suspendedO2Str.toDouble();
            double suspendedCO = suspendedCOStr.toDouble();

            addDataPoint(suspendedO2, suspendedCO, temperature, voltage, current, switch1);
        } else {
            // 正常期间：使用实时数值
            addDataPoint(o2, co, temperature, voltage, current, switch1);
        }

        debug_printf("数据采集完成 - 时间=%.3f小时(%.1f分钟), O2=%.2f%%, CO=%.0fppm (数据已添加到内存缓冲区)\n",
                    relativeTimeHours, relativeTimeMinutes, o2, co);

        // 更新当前数据值
        m_currentTemperature = QString::number(temperature, 'f', 1) + "℃";
        m_currentVoltage = QString::number(voltage, 'f', 4) + "kPa";  // 压力值保留4位小数
        m_currentCurrent = QString::number(current, 'f', 3) + "A";

        // 表格数据每次采集都更新（与图表同步）
        // m_tableUpdateCounter++;  // 已禁用频率控制

        // 每次采集都更新表格数据
        // if (m_tableUpdateCounter >= TABLE_UPDATE_INTERVAL) {
        //     m_tableUpdateCounter = 0;  // 重置计数器

            // 添加表格数据（每次采集都更新，在反吹期间使用暂停前的O2和CO数值）
            if (m_isDataUpdateSuspended) {
                // 反吹期间：使用暂停前的O2和CO数值，其他参数使用实时数值
                // 从暂停的数值字符串中提取数字部分
                QString suspendedO2Str = m_suspendedO2Value;
                QString suspendedCOStr = m_suspendedCOValue;
                suspendedO2Str.remove("%");
                suspendedCOStr.remove("ppm");

                double suspendedO2 = suspendedO2Str.toDouble();
                double suspendedCO = suspendedCOStr.toDouble();

                addSmokeTableRow(suspendedO2, suspendedCO, temperature, voltage, current, switch1);
                debug_printf("监控系统: 反吹反馈运行中，表格数据使用暂停前的O2=%.2f, CO=%.0f (每3次采集更新)\n", suspendedO2, suspendedCO);
            } else {
                // 正常期间：使用实时数值
                addSmokeTableRow(o2, co, temperature, voltage, current, switch1);
                debug_printf("监控系统: 表格数据已更新 - O2=%.2f%%, CO=%.0fppm (每3次采集更新)\n", o2, co);
            }

            // Redis缓存已删除，无需预生成缓存
        // } else {
        //     debug_printf("监控系统: 表格更新计数器=%d/%d，跳过本次表格更新\n", m_tableUpdateCounter, TABLE_UPDATE_INTERVAL);
        // }
    } else {
        // 硬件未连接
        if (m_isDataConnected) {
            m_isDataConnected = false;
            m_connectionStatus = "烟气分析仪未连接";
            debug_printf("UI连接状态: 烟气分析仪未连接\n");
            emit dataConnectionChanged();
        }

        // 硬件未连接时设置默认值
        m_currentTemperature = "0.0℃";
        m_currentVoltage = "0.0000kPa";
        m_currentCurrent = "0.000A";
        // 不添加任何数据到图表和表格
    }

    emit smokeDataChanged();
    emit currentDataChanged();

    // 在硬件连接时发射图表更新信号（CSV模式下无需检查内存数据）
    if (hardwareConnected) {
        emit chartDataUpdated();
    }

    // 调试信息：输出当前状态（CSV模式）
    debug_printf("监控系统状态: 硬件连接=%s, 数据连接=%s, 表格数据行数=%d\n",
                hardwareConnected ? "是" : "否",
                m_isDataConnected ? "是" : "否",
                m_smokeTableData.size());
}

void MonitoringDataSource::loadBoilerList()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    m_boilerList.clear();

    // 从全局设备映射中获取烟气分析仪列表
    for (const auto& pair : boiler_map) {
        m_boilerList.append(QString::fromStdString(pair.first));
    }

    // 如果有烟气分析仪，设置第一个为默认选择
    if (!m_boilerList.isEmpty() && m_currentBoiler.isEmpty()) {
        m_currentBoiler = m_boilerList.first();
        debug_printf("设置默认烟气分析仪: '%s'\n", m_currentBoiler.toStdString().c_str());

        // 如果监控正在运行，立即检测默认设备的连接状态
        if (m_isRunning) {
            debug_printf("默认设备设置: 立即检测连接状态\n");
            updateData();
        }
    }

    emit boilerListChanged();
    emit currentBoilerChanged();
#else
    // 如果硬件数据采集被禁用，提供默认烟气分析仪列表
    m_boilerList << "SmokeAnalyzer1" << "SmokeAnalyzer2";
    if (m_currentBoiler.isEmpty()) {
        m_currentBoiler = "SmokeAnalyzer1";
    }
    emit boilerListChanged();
    emit currentBoilerChanged();
#endif
}



void MonitoringDataSource::addSmokeTableRow(double o2, double co, double temperature, double voltage, double current, int switch1)
{
    QVariantMap row;
    row["time"] = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    row["o2"] = QString::number(o2, 'f', 3);
    row["co"] = QString::number(co, 'f', 0);
    row["temperature"] = QString::number(temperature, 'f', 1);
    row["voltage"] = QString::number(voltage, 'f', 1);
    row["current"] = QString::number(current, 'f', 3);
    row["switch1"] = QString::number(switch1);

    m_smokeTableData.prepend(row);

    // 保持最多5行数据
    if (m_smokeTableData.size() > MAX_TABLE_ROWS) {
        m_smokeTableData.removeLast();
    }

    debug_printf("表格数据已添加: 时间=%s, O2=%.3f%%, CO=%.0fppm, 总行数=%d (每3次采集更新)\n",
                row["time"].toString().toStdString().c_str(),
                o2, co, m_smokeTableData.size());

    emit smokeTableDataChanged();
}


// updateSmokeChartSeries方法已删除，只保留updateSmokeChartSeriesWithMinutes

// updateChartFromCsv方法已删除，使用内存数据

void MonitoringDataSource::updateSmokeChartSeriesWithMinutes(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int timeRangeMinutes)
{
    if (!o2Series || !coSeries) {
        debug_printf("图表更新: 系列指针为空\n");
        return;
    }

    // 转换为LineSeries
    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        debug_printf("图表更新: 系列类型转换失败\n");
        return;
    }

    // 使用高效的replace方法而不是clear+append
    QList<QPointF> o2Points;
    QList<QPointF> coPoints;

    // 获取对应时间范围的缓冲区
    TimeRangeBuffer& buffer = getBufferForTimeRange(timeRangeMinutes);

    // 固定3秒采集间隔
    const double SAMPLE_INTERVAL_MINUTES = 3.0 / 60.0;  // 3秒 = 0.05分钟

    // 计算需要显示的数据点数
    int requestedPoints = qMin(timeRangeMinutes * 60 / 3, buffer.dataCount);  // 每3秒一个点

    // 构建数据点列表
    for (int i = 0; i < requestedPoints; ++i) {
        int index = (buffer.currentIndex - requestedPoints + i + buffer.maxSize) % buffer.maxSize;
        const DataPoint &point = buffer.dataBuffer[index];

        // X轴时间：从0开始，每3秒一个点
        double timeMinutes = i * SAMPLE_INTERVAL_MINUTES;

        o2Points.append(QPointF(timeMinutes, point.o2));
        coPoints.append(QPointF(timeMinutes, point.co));
    }

    // 使用replace方法一次性更新所有数据点，这比逐个append更高效
    o2LineSeries->replace(o2Points);
    coLineSeries->replace(coPoints);

    debug_printf("高效更新完成: O2数据点=%d, CO数据点=%d (%d分钟范围, 3秒间隔)\n",
                o2Points.size(), coPoints.size(), timeRangeMinutes);
}


void MonitoringDataSource::reinitializeSerialConnection(const QString &oldBoiler, const QString &newBoiler)
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    if (newBoiler.isEmpty()) {
        debug_printf("新烟气分析仪名称为空，跳过串口重新初始化\n");
        return;
    }

    std::string newDeviceName = newBoiler.toStdString();
    debug_printf("开始为烟气分析仪 '%s' 重新初始化串口连接\n", newDeviceName.c_str());

    // 查找新烟气分析仪
    auto newIt = boiler_map.find(newDeviceName);
    if (newIt == boiler_map.end() || newIt->second == nullptr) {
        debug_printf("错误: 找不到烟气分析仪 '%s'，设备映射大小: %zu\n", newDeviceName.c_str(), boiler_map.size());
        return;
    }

    Boiler* newDeviceObj = newIt->second;
    debug_printf("找到烟气分析仪 '%s'，当前fd: %d，协议: '%s'\n",
                newDeviceName.c_str(), newDeviceObj->fd, newDeviceObj->protocol.c_str());

    // 检查新烟气分析仪的串口连接状态
    if (newDeviceObj->fd < 0) {
        // 串口未连接，需要重新初始化
        debug_printf("烟气分析仪 '%s' 串口未连接，开始重新初始化串口连接\n", newDeviceName.c_str());

        // 获取配置管理器
        extern ConfigManager* g_config_manager;
        if (g_config_manager == nullptr) {
            debug_printf("错误: 全局配置管理器未初始化\n");
            return;
        }

        try {
            // 重新获取协议配置并初始化串口
            std::string protocol = newDeviceObj->protocol;
            if (protocol.empty()) {
                // 如果协议为空，从配置文件重新读取
                protocol = g_config_manager->get<std::string>(newDeviceName, "Protocol");
                newDeviceObj->protocol = protocol;
                debug_printf("从配置文件重新读取烟气分析仪 '%s' 的协议: '%s'\n", newDeviceName.c_str(), protocol.c_str());
            }

            // 获取协议对应的串口配置
            std::string port = g_config_manager->get<std::string>(protocol, "Port");
            int baud_rate = g_config_manager->get<int>(protocol, "BaudRate", 9600);
            char parity = g_config_manager->get<char>(protocol, "Parity");
            int stop_bits = g_config_manager->get<int>(protocol, "StopBits", 1);
            int data_bits = g_config_manager->get<int>(protocol, "DataBits", 8);

            debug_printf("重新初始化烟气分析仪 '%s' 串口配置:\n", newDeviceName.c_str());
            debug_printf("  协议: '%s'\n", protocol.c_str());
            debug_printf("  端口: '%s'\n", port.c_str());
            debug_printf("  波特率: %d\n", baud_rate);
            debug_printf("  校验位: %c\n", parity);
            debug_printf("  停止位: %d\n", stop_bits);
            debug_printf("  数据位: %d\n", data_bits);

            // 重新打开串口
            extern int open_serial_port(const char *device, int speed, char parity, int stop_bits, int data_bits);
            int new_fd = open_serial_port(port.c_str(), baud_rate, parity, stop_bits, data_bits);

            if (new_fd >= 0) {
                newDeviceObj->fd = new_fd;
                debug_printf("烟气分析仪 '%s' 串口重新初始化成功，文件描述符: %d\n", newDeviceName.c_str(), new_fd);
            } else {
                debug_printf("烟气分析仪 '%s' 串口重新初始化失败\n", newDeviceName.c_str());
            }
        } catch (const std::exception& e) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生异常: %s\n", newDeviceName.c_str(), e.what());
        } catch (...) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生未知异常\n", newDeviceName.c_str());
        }
    } else {
        debug_printf("烟气分析仪 '%s' 串口已连接，文件描述符: %d\n", newDeviceName.c_str(), newDeviceObj->fd);
    }

    // 输出切换完成信息
    if (!oldBoiler.isEmpty()) {
        debug_printf("烟气分析仪切换完成: 从 '%s' 切换到 '%s'\n", oldBoiler.toStdString().c_str(), newDeviceName.c_str());
    } else {
        debug_printf("初始化烟气分析仪 '%s' 完成\n", newDeviceName.c_str());
    }

#else
    debug_printf("硬件数据采集被禁用，跳过串口重新初始化\n");
#endif
}

void MonitoringDataSource::updateTimerInterval()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    extern ConfigManager* g_config_manager;

    if (g_config_manager == nullptr) {
        debug_printf("⚠ 配置管理器未初始化，无法设置UI更新间隔\n");
        return;
    }

    // 如果没有选择锅炉，尝试从第一个可用锅炉获取配置
    std::string boilerName;
    if (m_currentBoiler.isEmpty()) {
        if (!boiler_map.empty()) {
            boilerName = boiler_map.begin()->first;
            debug_printf("调试: 没有选择锅炉，使用第一个可用锅炉 '%s' 的配置\n", boilerName.c_str());
        } else {
            debug_printf("⚠ 没有可用的锅炉配置\n");
            return;
        }
    } else {
        boilerName = m_currentBoiler.toStdString();
    }

    // 首先尝试从锅炉对象获取采集间隔
    auto it = boiler_map.find(boilerName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
        int collectionInterval = it->second->collection_interval;
        int uiInterval = collectionInterval * 1000; // 转换为毫秒，与采集间隔保持一致
        m_timer->setInterval(uiInterval);
        debug_printf("设置UI更新间隔: %d毫秒 (采集间隔: %d秒)\n", uiInterval, collectionInterval);
        return;
    }

    // 如果锅炉对象不可用，直接从配置文件读取
    if (g_config_manager->exists(boilerName, "CollectionInterval")) {
        int collectionInterval = g_config_manager->get<int>(boilerName, "CollectionInterval");
        if (collectionInterval > 0) {
            int uiInterval = collectionInterval * 1000; // 转换为毫秒，与采集间隔保持一致
            m_timer->setInterval(uiInterval);
            debug_printf("设置UI更新间隔: %d毫秒 (采集间隔: %d秒)\n", uiInterval, collectionInterval);
            return;
        }
    }
#endif
}

void MonitoringDataSource::updateChartIncremental(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int timeRangeMinutes)
{
    if (!o2Series || !coSeries) {
        return;
    }

    // 转换为LineSeries
    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 获取对应时间范围的缓冲区
    TimeRangeBuffer& buffer = getBufferForTimeRange(timeRangeMinutes);

    // 如果没有数据，直接返回
    if (buffer.dataCount == 0) {
        return;
    }

    // 检查是否需要更新图表
    int currentSeriesCount = o2LineSeries->count();

    // 计算需要显示的数据点数
    int requestedPoints = qMin(timeRangeMinutes * 60 / 3, buffer.dataCount);  // 每3秒一个点

    // 如果缓冲区已满，总是使用全量更新来实现滑动窗口
    if (buffer.dataCount >= buffer.maxSize) {
        // 数据已满，使用全量更新实现滑动窗口效果
        updateSmokeChartSeriesWithMinutes(o2Series, coSeries, timeRangeMinutes);
        return;
    }

    // 如果图表点数已经等于或超过请求的数据点数，说明没有新数据
    if (currentSeriesCount >= requestedPoints) {
        return; // 没有新数据需要添加
    }

    // 对于未满的情况，使用增量更新
    // 添加从当前图表点数到请求数据点数之间的所有新数据点
    for (int i = currentSeriesCount; i < requestedPoints; i++) {
        int dataIndex = (buffer.currentIndex - requestedPoints + i + buffer.maxSize) % buffer.maxSize;
        const DataPoint &point = buffer.dataBuffer[dataIndex];

        // 固定3秒采集间隔计算时间
        const double SAMPLE_INTERVAL_MINUTES = 3.0 / 60.0;  // 3秒 = 0.05分钟
        double timeMinutes = i * SAMPLE_INTERVAL_MINUTES;

        o2LineSeries->append(timeMinutes, point.o2);
        coLineSeries->append(timeMinutes, point.co);
    }

    static int updateCount = 0;
    updateCount++;
    if (updateCount % 20 == 0) {
        debug_printf("增量更新: 图表点数=%d, 缓冲区数据=%d (%d分钟范围, 3秒间隔)\n",
                    o2LineSeries->count(), buffer.dataCount, timeRangeMinutes);
    }
}

// ==================== 历史数据相关方法实现 ====================

QVariantList MonitoringDataSource::getAvailableHistoryDates() const
{
    return m_csvReader->getAvailableDates();
}

bool MonitoringDataSource::loadHistoryData(const QDate &date)
{
    if (!m_csvReader->hasDataForDate(date)) {
        return false;
    }

    m_historyData = m_csvReader->readDataByDate(date);
    m_currentHistoryDate = date;
    m_isHistoryMode = true;

    debug_printf("加载历史数据: 日期=%s, 数据点数=%d\n",
                date.toString("yyyy-MM-dd").toStdString().c_str(),
                m_historyData.size());

    return true;
}

int MonitoringDataSource::getHistoryDataPointCount(const QDate &date) const
{
    if (!m_csvReader->hasDataForDate(date)) {
        return 0;
    }

    // 读取数据并返回点数（去掉表头）
    QVariantList data = m_csvReader->readDataByDate(date);
    return data.size();
}

void MonitoringDataSource::switchToRealTimeMode()
{
    m_isHistoryMode = false;
    m_historyData.clear();
    m_currentHistoryDate = QDate();

    debug_printf("切换到实时模式\n");

    // 发射信号通知QML清理图表并重新加载实时数据
    emit smokeDataChanged();
    emit chartDataUpdated();
}

void MonitoringDataSource::updateHistoryChartWithSlider(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int timeRangeMinutes, double sliderPosition)
{
    if (!o2Series || !coSeries || !m_isHistoryMode || m_historyData.isEmpty()) {
        return;
    }

    // 转换为LineSeries
    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();

    // 根据时间范围确定需要的点数（使用预定义的缓冲区大小）
    int pointsNeeded;
    switch (timeRangeMinutes) {
        case 60:   pointsNeeded = 1200; break;   // 1小时
        case 480:  pointsNeeded = 9600; break;   // 8小时
        case 720:  pointsNeeded = 14400; break;  // 12小时
        case 1440: pointsNeeded = 28800; break;  // 24小时
        default:   pointsNeeded = 1200; break;   // 默认1小时
    }

    int totalPoints = m_historyData.size();

    // 根据滑块位置计算起始点
    int maxStartIndex = qMax(0, totalPoints - pointsNeeded);
    int startIndex = qRound(sliderPosition * maxStartIndex);

    // 确保不会超出数据范围
    int endIndex = qMin(startIndex + pointsNeeded, totalPoints);
    int actualPointsToShow = endIndex - startIndex;

    // 直接使用索引作为时间轴（每个点代表3秒间隔）
    for (int i = 0; i < actualPointsToShow; i++) {
        int dataIndex = startIndex + i;
        if (dataIndex < totalPoints) {
            QVariantMap dataPoint = m_historyData[dataIndex].toMap();
            double timeMinutes = i * 3.0 / 60.0;  // 每个点3秒，转换为分钟

            o2LineSeries->append(timeMinutes, dataPoint["O2(%)"].toDouble());
            coLineSeries->append(timeMinutes, dataPoint["CO(ppm)"].toDouble());
        }
    }

    debug_printf("历史数据图表更新: 时间范围=%d分钟, 需要点数=%d, 滑块位置=%.2f, 起始索引=%d, 显示点数=%d/%d\n",
                timeRangeMinutes, pointsNeeded, sliderPosition, startIndex, o2LineSeries->count(), totalPoints);
}

